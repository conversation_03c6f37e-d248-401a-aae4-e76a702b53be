/* Switch Example

   This example code is in the Public Domain (or CC0 licensed, at your option.)

   Unless required by applicable law or agreed to in writing, this
   software is distributed on an "AS IS" BASIS, WITHOUT WARRANTIES OR
   CONDITIONS OF ANY KIND, either express or implied.
*/

#include <string.h>
#include <inttypes.h>
#include <freertos/FreeRTOS.h>
#include <freertos/task.h>
#include <esp_log.h>
#include <esp_event.h>
#include <nvs_flash.h>

#include <esp_rmaker_core.h>
#include <esp_rmaker_console.h>
#include <app_network.h>
#include <app_insights.h>
#include <iot_button.h>
#include "app_priv.h"

static const char *TAG = APP_TAG;

/* this organizes most of rmaker related into one file */
extern esp_rmaker_node_t* rmaker_main(void);

void app_main()
{
    /* Initialize Application specific hardware drivers and
     * set initial state.
     */
    esp_rmaker_console_init();
    app_driver_init();

    /* Initialize NVS. */
    esp_err_t err = nvs_flash_init();
    if (err == ESP_ERR_NVS_NO_FREE_PAGES || err == ESP_ERR_NVS_NEW_VERSION_FOUND) {
        ESP_ERROR_CHECK(nvs_flash_erase());
        err = nvs_flash_init();
    }
    ESP_ERROR_CHECK( err );

    /* Initialize Wi-Fi. Note that, this should be called before esp_rmaker_node_init()
     */
    app_network_init();

    esp_rmaker_node_t* node = rmaker_main();
    if (!node) {
        ESP_LOGE(TAG, "Could not initialise node. Aborting!!!");
        vTaskDelay(5000/portTICK_PERIOD_MS);
        abort();
    }

    if (!app_driver_4G_connect()) {
        /* TODO: call app_network_set_custom_mfg_data to setup our specific BLE boradcasting package compatible with yuanbao? */
        // err = app_network_set_custom_mfg_data(MGF_DATA_DEVICE_TYPE_SWITCH, MFG_DATA_DEVICE_SUBTYPE_SWITCH);
        
        /* Start the Wi-Fi.
        * If the node is provisioned, it will start connection attempts,
        * else, it will start Wi-Fi provisioning. The function will return
        * after a connection has been successfully established
        */
        err = app_network_start(POP_TYPE_NONE);
        if (err != ESP_OK) {
            ESP_LOGE(TAG, "Could not start Wifi. Aborting!!!");
            vTaskDelay(5000/portTICK_PERIOD_MS);
            abort();
        }
    }

    if (audio_app_connect() != ESP_OK) {
        ESP_LOGE(TAG, "Failed to connect the audio app server side");
    } else {
        ESP_LOGI(TAG, " Success to connect the audio app server side");
    }
}
