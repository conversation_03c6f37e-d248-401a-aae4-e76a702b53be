/* Switch demo implementation using button and RGB LED
   
   This example code is in the Public Domain (or CC0 licensed, at your option.)

   Unless required by applicable law or agreed to in writing, this
   software is distributed on an "AS IS" BASIS, WITHOUT WARRANTIES OR
   CONDITIONS OF ANY KIND, either express or implied.
*/

#include <sdkconfig.h>

#include <iot_button.h>
#include <esp_log.h>
#include <app_reset.h>
#include <board.h>
#include <media_lib_adapter.h>

#include "app_priv.h"


/* This is the button that is used for toggling the power */
#define BUTTON_HELP_GPIO        VEB_IO_KEY_SECOND
#define BUTTON_HELP_GPIO_PULL   VEB_IO_KEY

#define BUTTON_ACTIVE_LEVEL     VEB_IO_KEY_ACTIVE_LEVEL

/* Our buttons are so easy to push that we have to use really long time for WIFI and factory reset */
#define WIFI_RESET_BUTTON_TIMEOUT       15
#define FACTORY_RESET_BUTTON_TIMEOUT    60

static const char* TAG = "APP_DRIVER";

static void help_btn_cb(void *arg)
{
    ESP_LOGI(TAG, "triggring the help");
#ifdef CONFIG_RADAR_BOARD_KORVO2
    ESP_LOGE(TAG, "the ADC button is not supported yt");
    return;
#endif   
    if (audio_app_trigger() != ESP_OK) {
        ESP_LOGI(TAG, "triggering help me error");
    }
}

static void init_buttons()
{
    button_handle_t btn_pull = NULL;

    button_handle_t btn_main = iot_button_create(BUTTON_HELP_GPIO, BUTTON_ACTIVE_LEVEL);
    if (BUTTON_HELP_GPIO_PULL > 0) {
        btn_pull = iot_button_create(BUTTON_HELP_GPIO_PULL, BUTTON_ACTIVE_LEVEL);
    }

    if (btn_main) {
        /* Register a callback for a BUTTON_CB_PUSH (Don't know why BUTTON_CB_TAP (short press) causes a reboot */
        ESP_ERROR_CHECK(iot_button_set_evt_cb(btn_main, BUTTON_CB_TAP, help_btn_cb, NULL));
        
        /** register a long press button callback for cancel the call 
         * iot_button_add_on_press_cb(btn_main, 1000000, NULL, NULL); */

    }

    if (btn_pull) {
        /* Register a callback for a button tap (short press) event */
        iot_button_set_evt_cb(btn_pull, BUTTON_CB_TAP, help_btn_cb, NULL);
    }
}

void app_driver_init()
{
    init_buttons();
}


bool app_driver_4G_connect(void) 
{
    // TODO LF: implement this with protocol_examples_common as a reference
    return false;
}
