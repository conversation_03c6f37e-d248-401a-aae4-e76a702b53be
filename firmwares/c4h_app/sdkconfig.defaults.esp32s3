CONFIG_IDF_TARGET="esp32s3"
CONFIG_IDF_TARGET_ESP32S3=y

#
# Serial flasher config
#
CONFIG_ESPTOOLPY_FLASHFREQ_80M=y
CONFIG_ESPTOOLPY_FLASHFREQ="80m"

#
# ESP32S3-Specific
#
CONFIG_ESP_DEFAULT_CPU_FREQ_MHZ=240

#
# SPI RAM config
#
CONFIG_SPIRAM=y
CONFIG_SPIRAM_BOOT_INIT=y
CONFIG_SPIRAM_USE_MALLOC=y
CONFIG_SPIRAM_SPEED_80M=y
CONFIG_SPIRAM_RODATA=y
CONFIG_SPIRAM_FETCH_INSTRUCTIONS=y
CONFIG_SPIRAM_MALLOC_ALWAYSINTERNAL=2048
CONFIG_SPIRAM_MALLOC_RESERVE_INTERNAL=18432
CONFIG_SPIRAM_ALLOW_BSS_SEG_EXTERNAL_MEMORY=y
CONFIG_SPIRAM_TRY_ALLOCATE_WIFI_LWIP=y
CONFIG_SPIRAM_MODE_OCT=y

CONFIG_SR_WN_WN9_XIAOAITONGXUE=y

#
# FreeRTOS
# Kernel
#
CONFIG_FREERTOS_TASK_CREATE_ALLOW_EXT_MEM=y

#
#   Don't know why it requires so large stack depth after rainmaker is 
#   enabled, increased from 2048 with +64 (we may try a smaller one). 
#   More strange thing is that we have to add 64 bytes more for the 
#   PRO board but 1024 bytes for the CFH board
# 
CONFIG_FREERTOS_TIMER_TASK_STACK_DEPTH=3072


CONFIG_C4H_SUPPORT_RAINMAKER=y