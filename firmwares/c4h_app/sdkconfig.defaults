# Serial flash
CONFIG_ESPTOOLPY_FLASHMODE_QIO=y
CONFIG_ESPTOOLPY_FLASHFREQ_80M=y
CONFIG_ESPTOOLPY_FLASHSIZE_8MB=y

CONFIG_SPI_FLASH_ROM_IMPL=y
CONFIG_FREERTOS_ENABLE_BACKWARD_COMPATIBILITY=y

#
# Partition Table
#
CONFIG_PARTITION_TABLE_CUSTOM=y
CONFIG_PARTITION_TABLE_CUSTOM_FILENAME="partitions.csv"
CONFIG_PARTITION_TABLE_FILENAME="partitions.csv"

#
# Wi-Fi
#
CONFIG_ESP_WIFI_STATIC_RX_BUFFER_NUM=9
CONFIG_ESP_WIFI_DYNAMIC_RX_BUFFER_NUM=128
CONFIG_ESP_WIFI_STATIC_TX_BUFFER_NUM=9
CONFIG_ESP_WIFI_IRAM_OPT=n
CONFIG_ESP_WIFI_RX_IRAM_OPT=n
CONFIG_MBEDTLS_EXTERNAL_MEM_ALLOC=y

#
# LWIP
#
CONFIG_LWIP_UDP_RECVMBOX_SIZE=64
CONFIG_LWIP_MAX_UDP_PCBS=64
CONFIG_LWIP_TCP_SND_BUF_DEFAULT=65535
CONFIG_LWIP_TCP_WND_DEFAULT=65535
CONFIG_LWIP_TCP_RECVMBOX_SIZE=64
CONFIG_LWIP_TCPIP_RECVMBOX_SIZE=64

#
# ESP Speech Recognition
#
CONFIG_MODEL_IN_FLASH=y
# end of ESP Speech Recognition

#
# FreeRTOS
#
CONFIG_FREERTOS_HZ=1000
CONFIG_FREERTOS_USE_TRACE_FACILITY=y
CONFIG_FREERTOS_USE_STATS_FORMATTING_FUNCTIONS=y
CONFIG_FREERTOS_VTASKLIST_INCLUDE_COREID=y
CONFIG_FREERTOS_GENERATE_RUN_TIME_STATS=y
CONFIG_FREERTOS_PLACE_FUNCTIONS_INTO_FLASH=y

#
# Certificate Bundle
#
# CONFIG_MBEDTLS_ROM_MD5 is not set
CONFIG_ESP_TLS_INSECURE=y
CONFIG_ESP_TLS_SKIP_SERVER_CERT_VERIFY=y

#
#   BLE Configuration
# 
CONFIG_BT_ENABLED=y
CONFIG_BT_NIMBLE_ENABLED=y

# Other
#
CONFIG_ESP_SYSTEM_EVENT_TASK_STACK_SIZE=3072


CONFIG_AUDIO_BOARD_CUSTOM=y

#
#   RainMaker
#
CONFIG_ESP_RMAKER_OTA_AUTOFETCH=n
CONFIG_ESP_RMAKER_SCHEDULING_MAX_SCHEDULES=3
CONFIG_ESP_RMAKER_OTA_TIME_SUPPORT=n

#
#   Don't know why it requires so large stack depth after rainmaker is 
#   enabled, increased from 2048 with +64 (we may try a smaller one). 
#   More strange thing is that we have to add 64 bytes more for the PRO board.
# 
CONFIG_FREERTOS_TIMER_TASK_STACK_DEPTH=2176