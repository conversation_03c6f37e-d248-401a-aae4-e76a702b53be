#!/bin/bash

get_abs_filename() {
  # $1 : relative filename
  echo "$(cd "$(dirname "$1")" && pwd)/$(basename "$1")"
}

# 选择conda环境
if [[ `whoami`=="lux" ]]; then
  echo `whoami`
  . ~/.bashrc_conda
fi

# setup a ESP_ROOT containing all products of Espressif
ESP_ROOT=~/esp
export ADF_PATH=$ESP_ROOT/esp-adf
. $ADF_PATH/export.sh

alias idf="idf.py"
alias idf-flash="idf.py -b 921600 flash"
alias set-target-esp32s3="idf set-target esp32s3"