{"cells": [{"cell_type": "code", "execution_count": 1, "id": "9e2b4b23-445e-46e6-bb26-9c3eaa81c01e", "metadata": {}, "outputs": [], "source": ["text = '''\n", "W (5152) ES8388_DRIVER: 0: 12\n", "W (5152) ES8388_DRIVER: 1: 50\n", "W (5162) ES8388_DRIVER: 2: 0\n", "W (5162) ES8388_DRIVER: 3: 0\n", "W (5162) ES8388_DRIVER: 4: 28\n", "W (5172) ES8388_DRIVER: 5: 0\n", "W (5172) ES8388_DRIVER: 6: 0\n", "W (5172) ES8388_DRIVER: 7: 7c\n", "W (5182) ES8388_DRIVER: 8: 0\n", "W (5182) ES8388_DRIVER: 9: bb\n", "W (5192) ES8388_DRIVER: a: 0\n", "W (5192) ES8388_DRIVER: b: 2\n", "W (5192) ES8388_DRIVER: c: c\n", "W (5202) ES8388_DRIVER: d: 2\n", "W (5202) ES8388_DRIVER: e: 30\n", "W (5202) ES8388_DRIVER: f: 20\n", "W (5212) ES8388_DRIVER: 10: 0\n", "W (5212) ES8388_DRIVER: 11: 0\n", "W (5212) ES8388_DRIVER: 12: 78\n", "W (5222) ES8388_DRIVER: 13: b0\n", "W (5222) ES8388_DRIVER: 14: 32\n", "W (5232) ES8388_DRIVER: 15: 6\n", "W (5232) ES8388_DRIVER: 16: 3\n", "W (5232) ES8388_DRIVER: 17: 18\n", "W (5242) ES8388_DRIVER: 18: 2\n", "W (5242) ES8388_DRIVER: 19: 4\n", "W (5242) ES8388_DRIVER: 1a: 2a\n", "W (5252) ES8388_DRIVER: 1b: 2a\n", "W (5252) ES8388_DRIVER: 1c: 8\n", "W (5252) ES8388_DRIVER: 1d: 0\n", "W (5262) ES8388_DRIVER: 1e: 1f\n", "W (5262) ES8388_DRIVER: 1f: f7\n", "W (5272) ES8388_DRIVER: 20: fd\n", "W (5272) ES8388_DRIVER: 21: ff\n", "W (5272) ES8388_DRIVER: 22: 1f\n", "W (5282) ES8388_DRIVER: 23: f7\n", "W (5282) ES8388_DRIVER: 24: fd\n", "W (5282) ES8388_DRIVER: 25: ff\n", "W (5292) ES8388_DRIVER: 26: 0\n", "W (5292) ES8388_DRIVER: 27: 90\n", "W (5302) ES8388_DRIVER: 28: 28\n", "W (5302) ES8388_DRIVER: 29: 28\n", "W (5302) ES8388_DRIVER: 2a: 90\n", "W (5312) ES8388_DRIVER: 2b: 80\n", "W (5312) ES8388_DRIVER: 2c: 0\n", "W (5312) ES8388_DRIVER: 2d: 0\n", "W (5322) ES8388_DRIVER: 2e: 1e\n", "W (5322) ES8388_DRIVER: 2f: 1e\n", "W (5332) ES8388_DRIVER: 30: 0\n", "W (5332) ES8388_DRIVER: 31: 0\n", "\n", "'''"]}, {"cell_type": "code", "execution_count": 2, "id": "85f13217-4e65-475c-8f57-ebb6632b37bc", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Register 0 0001 0010\n", "Register 1 0101 0000\n", "Register 2 0000 0000\n", "Register 3 0000 0000\n", "Register 4 0010 1000\n", "Register 5 0000 0000\n", "Register 6 0000 0000\n", "Register 7 0111 1100\n", "Register 8 0000 0000\n", "Register 9 1011 1011\n", "Register 10 0000 0000\n", "Register 11 0000 0010\n", "Register 12 0000 1100\n", "Register 13 0000 0010\n", "Register 14 0011 0000\n", "Register 15 0010 0000\n", "Register 16 0000 0000\n", "Register 17 0000 0000\n", "Register 18 0111 1000\n", "Register 19 1011 0000\n", "Register 20 0011 0010\n", "Register 21 0000 0110\n", "Register 22 0000 0011\n", "Register 23 0001 1000\n", "Register 24 0000 0010\n", "Register 25 0000 0100\n", "Register 26 0010 1010\n", "Register 27 0010 1010\n", "Register 28 0000 1000\n", "Register 29 0000 0000\n", "Register 30 0001 1111\n", "Register 31 1111 0111\n", "Register 32 1111 1101\n", "Register 33 1111 1111\n", "Register 34 0001 1111\n", "Register 35 1111 0111\n", "Register 36 1111 1101\n", "Register 37 1111 1111\n", "Register 38 0000 0000\n", "Register 39 1001 0000\n", "Register 40 0010 1000\n", "Register 41 0010 1000\n", "Register 42 1001 0000\n", "Register 43 1000 0000\n", "Register 44 0000 0000\n", "Register 45 0000 0000\n", "Register 46 0001 1110\n", "Register 47 0001 1110\n", "Register 48 0000 0000\n", "Register 49 0000 0000\n"]}], "source": ["for t in text.split('W '):\n", "    line = t.split(':')[-2:]\n", "    if len(line) < 2: continue\n", "        \n", "    a, b = line\n", "    b = b.strip()\n", "\n", "    binary_str = bin(int(b, 16))[2:]\n", "    padded_binary_str = binary_str.zfill(8)\n", "    # 按每 4 位添加空格\n", "    result = ' '.join(padded_binary_str[i:i+4] for i in range(0, len(padded_binary_str), 4))    \n", "    print(f'Register {int(a, 16)}', result)\n", "    "]}, {"cell_type": "code", "execution_count": null, "id": "a4be5ee3-1d43-432a-a18b-471c9a8dc16f", "metadata": {}, "outputs": [], "source": [" "]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.9"}}, "nbformat": 4, "nbformat_minor": 5}