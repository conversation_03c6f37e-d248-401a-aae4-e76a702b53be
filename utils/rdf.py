
import sys, os, shutil, argparse
from pathlib import Path


def _clean_projects(root_dir:str=None, build_name='build', flag_file_name='CMakeFiles'):
    """
    Delete all directories named 'build' under the given root directory.
    If a 'build' directory is not empty, it will only be deleted if it contains a directory named as specified by flag_file_name.

    :param root_dir: The root directory to start the search from.
    :param flag_file_name: The name of the sub - directory that a non - empty 'build' directory must contain to be deleted. Default is 'xxx'.
    """
    if root_dir is None: root_dir = Path.cwd()
    # Traverse all files and sub - directories under the specified directory
    for root, dirs, files in os.walk(root_dir, topdown=False):
        for dir_name in dirs:
            if dir_name == build_name:
                build_dir = os.path.join(root, dir_name)
                if os.listdir(build_dir):
                    specific_sub_dir = os.path.join(build_dir, flag_file_name)
                    if os.path.isdir(specific_sub_dir): shutil.rmtree(build_dir)
                else: os.rmdir(build_dir)

if __name__ == '__main__':
    parser = argparse.ArgumentParser()
    parser.add_argument('--auto_prefix', default=False, type=bool)
    _clean_projects()
