# Edit following two lines to set component requirements (see docs)
set(COMPONENT_REQUIRES)
set(COMPONENT_PRIV_REQUIRES audio_sal audio_hal esp_dispatcher esp_peripherals display_service esp_lcd_ili9341)

if (CONFIG_RADAR_BOARD_PRO)
    add_definitions(-DRADAR_BOARD_NAME="PRO")
    list(APPEND COMPONENT_ADD_INCLUDEDIRS ./radar_boards/pro ./audio_boards/pro)
    set(COMPONENT_SRCS
            ./audio_boards/pro/board.c
            ./audio_boards/pro/board_pins_config.c
    )
endif()

if (CONFIG_RADAR_BOARD_KORVO2V3)
    add_definitions(-DRADAR_BOARD_NAME="KORVO2V3")
    list(APPEND COMPONENT_ADD_INCLUDEDIRS ./radar_boards/korvo2v3 ./audio_boards/korvo2v3)
    set(COMPONENT_SRCS
            ./audio_boards/korvo2v3/board.c
            ./audio_boards/korvo2v3/board_pins_config.c
    )
endif()

if (CONFIG_RADAR_BOARD_CFH)
    add_definitions(-DRADAR_BOARD_NAME="CFH")
    list(APPEND COMPONENT_ADD_INCLUDEDIRS ./radar_boards/cfh/ ./audio_boards/cfh)
    set(COMPONENT_SRCS
            ./audio_boards/cfh/board.c
            ./audio_boards/cfh/board_pins_config.c
    )
endif()

list(APPEND COMPONENT_ADD_INCLUDEDIRS ./radar_boards/)
list(APPEND COMPONENT_SRCS ./radar_boards/radar_board.c)

register_component()
idf_component_get_property(audio_board_lib audio_board COMPONENT_LIB)
set_property(TARGET ${audio_board_lib} APPEND PROPERTY INTERFACE_LINK_LIBRARIES ${COMPONENT_LIB})