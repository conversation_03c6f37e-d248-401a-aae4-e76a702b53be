#ifndef __VEB_IO_DEF_H__
#define __VEB_IO_DEF_H__

/**
 * User Interaction
 */
#define VEB_IO_KEY              (33)
#define VEB_IO_KEY_SECOND       (-1)
#define VEB_IO_LED              (48)

#define VEB_IO_KEY_ACTIVE_LEVEL        (0)
#define VEB_IO_KEY_SECOND_ACTIVE_LEVEL (0)

/*
 * CLI UART
 */
#define VEB_CLI_UART_NUM   (0)
#define VEB_IO_CLI_UART_TX (43)
#define VEB_IO_CLI_UART_RX (44)

/*
* Radar
*/
#define VEB_IO_RADAR_SPI_MISO  (13)
#define VEB_IO_RADAR_SPI_MOSI  (11)
#define VEB_IO_RADAR_SPI_CLK   (14)
#define VEB_IO_RADAR_SPI_CS    (15)
#define VEB_IO_RADAR_SPI_INT   (16)
#define VEB_IO_RADAR_FLASH_PWR (45)
// TODO lf, #define VEB_IO_RADAR_PWR_EN    ()
#define VEB_IO_RADAR_NRESET    (10)
#define VEB_IO_RADAR_CMD_TX    (9)
#define VEB_IO_RADAR_CMD_RX    (8)
#define VEB_IO_RADAR_DATA_RX   (12)
#define VEB_IO_RADAR_DATA_TX   (-1)
#define VEB_IO_RADAR_BOOT_CTL  (38)

/*
* Audio
*/
#define VEB_IO_AUD_I2S_DOUT (3)
#define VEB_IO_AUD_I2S_DIN  (4)
#define VEB_IO_AUD_I2S_LRCK (5)
#define VEB_IO_AUD_I2S_SCLK (6)
#define VEB_IO_AUD_I2S_MCLK (7)
#define VEB_IO_AUD_PA_CTL   (17)

/*
* USB for 4G modules
*/
#define VEB_IO_IOT_PWR_CTL  (21)
#define VEB_IO_IOT_UART_TX  (36)
#define VEB_IO_IOT_UART_RX  (37)
#define VEB_IO_IOT_USB_DM   (19)
#define VEB_IO_IOT_USB_DP   (20)
#define VEB_IO_IOT_RESET    (35)

/*
* Sensor
*/
#define VEB_IO_I2C_SDA      (2)
#define VEB_IO_I2C_SCL      (1)
#define VEB_IO_VEML6030_INT (18)
#define VEB_IO_BMI160_INT   (34)

#endif /* __VEB_IO_DEF_H__ */