#ifndef __VEB_IO_DEF_H__
#define __VEB_IO_DEF_H__

/** 
 * Radar Board Definitions (RBD)
 * 
 * This file defines all possible functions that for a radar application board. It serves as a 
 * template for defining a new radar board by using default value -1 and then forces the application
 * code to check its availibility. 
 *  
 */

/**
 * User Interaction
 */
#define VEB_IO_KEY              (-1)
#define VEB_IO_KEY_SECOND       (-1)
#define VEB_IO_LED              (-1)
#define VEB_IO_LED_SECOND       (-1)

#define VEB_IO_KEY_ACTIVE_LEVEL        (0)
#define VEB_IO_KEY_SECOND_ACTIVE_LEVEL (0)

/*
 * CLI UART
 */
#define VEB_CLI_UART_NUM   (0)
#define VEB_IO_CLI_UART_TX (43)
#define VEB_IO_CLI_UART_RX (44)


/*
* Radar
*/
#define VEB_IO_RADAR_SPI_MISO  (-1)
#define VEB_IO_RADAR_SPI_MOSI  (-1)
#define VEB_IO_RADAR_SPI_CLK   (-1)
#define VEB_IO_RADAR_SPI_CS    (-1)
#define VEB_IO_RADAR_SPI_INT   (-1)
#define VEB_IO_RADAR_FLASH_PWR (-1)
#define VEB_IO_RADAR_PWR_EN    (-1)
#define VEB_IO_RADAR_NRESET    (-1)
#define VEB_IO_RADAR_CMD_TX    (-1)
#define VEB_IO_RADAR_CMD_RX    (-1)
#define VEB_IO_RADAR_DATA_RX   (-1)
#define VEB_IO_RADAR_BOOT_CTL  (-1)

/*
* Audio 
*/
#define VEB_IO_AUD_I2S_DOUT (-1)
#define VEB_IO_AUD_I2S_DIN  (-1)
#define VEB_IO_AUD_I2S_LRCK (-1)
#define VEB_IO_AUD_I2S_SCLK (-1)
#define VEB_IO_AUD_I2S_MCLK (-1)
#define VEB_IO_AUD_PA_CTL   (-1)

/*
* USB for 4G modules
*/
#define VEB_IO_IOT_PWR_CTL  (-1)
#define VEB_IO_IOT_UART_TX  (-1)
#define VEB_IO_IOT_UART_RX  (-1)
#define VEB_IO_IOT_USB_DM   (-1)
#define VEB_IO_IOT_USB_DP   (-1)
#define VEB_IO_IOT_RESET    (-1)

/*
* Sensor
*/
#define VEB_IO_I2C_SDA      (-1)
#define VEB_IO_I2C_SCL      (-1)
#define VEB_IO_VEML6030_INT (-1)
#define VEB_IO_BMI160_INT   (-1)




#endif /* __VEB_IO_DEF_H__ */