#pragma once
#include <stdint.h>

typedef struct radar_stream
{
    int (*read_data)(uint8_t* buf_in, int buf_sz);
    int (*write_data)(uint8_t* buf_out, int in_data_sz);
    void* context;
} radar_data_stream_t;

typedef void* radar_board_handle;

/* return null if no radar is available on the board */
radar_board_handle radar_board_init(void);

/* get the command stream of radar */
radar_data_stream_t radar_board_cmd_stream(radar_board_handle handle);

/* return the data stream if the radar's specific data port is available */
radar_data_stream_t radar_board_data_stream(radar_board_handle handle);

/* return the spi stream is the radar's SPI is available */
radar_data_stream_t radar_board_spi_stream(radar_board_handle handle);

void radar_board_radar_reboot(radar_board_handle handle);
void radar_board_radar_flash(radar_board_handle handle);

void radar_board_deinit(radar_board_handle handle);