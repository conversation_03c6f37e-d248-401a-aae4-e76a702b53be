#pragma once
#include "esp_rtc.h"

typedef struct audio_voip*  voip_handle_t;
typedef int (*receive_audio_cb)(unsigned char *data, int len, void* ctx);

esp_err_t voip_init(voip_handle_t* handle, const char* uri, esp_rtc_event_handle event_handler, receive_audio_cb rec_cb, void* user_ctx);
esp_err_t voip_send(voip_handle_t handle, uint8_t* data, size_t sz); 
esp_err_t voip_sip(voip_handle_t handle, esp_rtc_handle_t* out);
esp_err_t voip_deinit(voip_handle_t handle);