#include "../record.h"
#include "../priv.h"

#define WAKEUP_REC_READING       (1 << 0)

typedef struct app_record
{
    recorder_pipeline_handle_t record_pipeline;
    audio_rec_handle_t         recorder_engine;
    on_data_ready_t            on_data_ready;
    EventGroupHandle_t         wakeup_event;
    /* data */
} app_record_t;



static void voice_read_task(void *args)
{
    app_record_t* handle = (app_record_t*) args;

    const int voice_data_read_sz = recorder_pipeline_get_default_read_size(handle->record_pipeline);
    uint8_t *voice_data = audio_calloc(1, voice_data_read_sz);
    bool runing = true;

#if defined (CONFIG_LANGUAGE_WAKEUP_MODE)
    TickType_t wait_tm = portMAX_DELAY;
#else
    TickType_t wait_tm = 0;
#endif // CONFIG_LANGUAGE_WAKEUP_MODE
    while (runing) {
    #if defined (CONFIG_LANGUAGE_WAKEUP_MODE)
        EventBits_t bits = xEventGroupWaitBits(handle->wakeup_event, WAKEUP_REC_READING , false, true, wait_tm);
        if (bits & WAKEUP_REC_READING) {
            int ret = audio_recorder_data_read(handle->recorder_engine, voice_data, voice_data_read_sz, portMAX_DELAY);
            if (ret == 0 || ret == -1) {
                if (ret == 0) {
                    vTaskDelay(15 / portTICK_PERIOD_MS);
                    continue;
                }
                ESP_LOGE(TAG, "audio_recorder_data_read failed, ret: %d\n", ret);
                xEventGroupClearBits(handle->wakeup_event,  WAKEUP_REC_READING);
            } else {
                if (handle->on_data_ready) handle->on_data_ready(voice_data, voice_data_read_sz);
            }
        }
    #else
        int read_len = audio_recorder_data_read(handle->recorder_engine, voice_data, voice_data_read_sz, portMAX_DELAY);
        if (read_len == voice_data_read_sz) {
            if (handle->on_data_ready) handle->on_data_ready(voice_data, read_len);
        }
    #endif
    }
    xEventGroupClearBits(handle->wakeup_event, WAKEUP_REC_READING);
    audio_free(voice_data);
    vTaskDelete(NULL);
}


esp_err_t record_init(record_handle_t* out, rec_event_cb_t rec_cb, on_data_ready_t data_cb)
{
    app_record_t *handle = audio_calloc(1, sizeof(app_record_t));
    handle->wakeup_event = xEventGroupCreate();
    handle->record_pipeline = recorder_pipeline_open();
    recorder_pipeline_run(handle->record_pipeline);
    vTaskDelay(pdMS_TO_TICKS(200));
    handle->recorder_engine = (audio_rec_handle_t) audio_record_engine_init(handle->record_pipeline, rec_cb);
    handle->on_data_ready = data_cb;

    *out = handle;
    return audio_thread_create(NULL, "voice_read_task", voice_read_task, handle, 5 * 1024, 5, true, 0);
}


esp_err_t record_notify_wakup(record_handle_t handle, bool up) 
{
    if (up) {
        return xEventGroupSetBits(handle->wakeup_event, WAKEUP_REC_READING);
    } else {
        return xEventGroupClearBits(handle->wakeup_event, WAKEUP_REC_READING);
    }
}

esp_err_t record_start(record_handle_t handle) 
{
    return audio_recorder_trigger_start(handle->recorder_engine);
}

esp_err_t record_stop(record_handle_t handle) 
{
    return audio_recorder_trigger_stop(handle->recorder_engine);
}