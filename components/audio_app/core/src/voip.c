#include "esp_netif.h"
#include "media_lib_adapter.h"
#include "media_lib_netif.h"
#include "esp_peripherals.h"
#include "../priv.h"
#include "../voip.h"

#define AUDIO_DATA_SENT (1 << 0)

typedef struct audio_voip { 
    volatile bool       using_audio;
    uint8_t             sending_buf[AUDIO_SUPPORT_REC_BUF_SIZE];
    size_t              sending_sz;
    EventGroupHandle_t  sent_evt;
    esp_rtc_handle_t    sip;
    void*                   user_ctx;
    esp_rtc_event_handle    event_handler;
    receive_audio_cb        rec_audio_cb;
} audio_voip_t;

static char *_get_network_ip()
{
    media_lib_ipv4_info_t ip_info;
    media_lib_netif_get_ipv4_info(MEDIA_LIB_NET_TYPE_STA, &ip_info);
    return media_lib_ipv4_ntoa(&ip_info.ip);
}

static int _send_audio(unsigned char *data, int len, void* ctx)
{
    voip_handle_t handle = (voip_handle_t) ctx;
    int ret = 0;

    if (handle->sending_sz > 0) {
        memcpy(data, (void*) &handle->sending_buf[0], handle->sending_sz);
        ret = handle->sending_sz;
    }
    /* trigger the data sent event any way*/
    xEventGroupSetBits(handle->sent_evt, AUDIO_DATA_SENT); 

    /* return the length of filled data*/
    return ret;
}

esp_err_t voip_send(voip_handle_t handle, uint8_t* data, size_t sz)
{
    ESP_RETURN_ON_FALSE(handle != NULL, ESP_ERR_INVALID_STATE, TAG, "the call  is not created yet ");
    if (!handle->using_audio) return ESP_OK;

    EventBits_t bits = xEventGroupWaitBits(handle->sent_evt, AUDIO_DATA_SENT, false, true, 0);
    if (bits & AUDIO_DATA_SENT) {
        handle->sending_sz = sz;
        if (sz >0) {
            memcpy(&handle->sending_buf[0], data, sz);
        }
        xEventGroupClearBits(handle->sent_evt, AUDIO_DATA_SENT);
        return ESP_OK;
    } else {
        return ESP_ERR_INVALID_STATE;
    }
}

static int _receive_audio(unsigned char *data, int len, void* ctx)
{
    voip_handle_t handle = (voip_handle_t) ctx;
    if ((len == 6) && !strncasecmp((char *)data, "DTMF-", 5)) {
        ESP_LOGI(TAG,"Receive DTMF Event ID : %d", data[5]);
        return 0;
    } else {
        if (!handle->rec_audio_cb) return 0;
        return handle->rec_audio_cb(data, len, handle->user_ctx);
    }
}

static int _handle_sip_event(esp_rtc_event_t event, void* ctx)
{
    voip_handle_t handle = (voip_handle_t) ctx;
    /* we should use this callback to call a list of a target */
    switch ((int)event) {
        case ESP_RTC_EVENT_AUDIO_SESSION_BEGIN:
            handle->using_audio = true;
            break;
        case ESP_RTC_EVENT_AUDIO_SESSION_END:
            handle->using_audio = false;
            break;
    }
    if (handle->event_handler) handle->event_handler(event, handle->user_ctx);
    return ESP_OK;
}

esp_err_t voip_init(voip_handle_t* handle, const char* uri, esp_rtc_event_handle event_handler, receive_audio_cb cb, void* user_ctx)
{

    esp_err_t ret = media_lib_add_default_adapter();
    if (ret != ESP_OK) return ret;

    voip_handle_t self = audio_calloc(1, sizeof(struct audio_voip));
    self->event_handler = event_handler;
    self->user_ctx = user_ctx;
    self->sent_evt = xEventGroupCreate();
    self->rec_audio_cb = cb;
    self->using_audio = false;

    esp_rtc_data_cb_t data_cb = {
        .send_audio = _send_audio,
        .receive_audio = _receive_audio,
    };
    esp_rtc_config_t sip_service_config = {
        .uri = uri,
        .ctx = self,
        .local_addr = _get_network_ip(),
        .acodec_type = RTC_ACODEC_NULL,
        .data_cb = &data_cb,
        .event_handler = _handle_sip_event,
    };

#if defined (CONFIG_AUDIO_SUPPORT_OPUS_DECODER)
    sip_service_config.acodec_type = RTC_ACODEC_OPUS;        
#elif defined (CONFIG_AUDIO_SUPPORT_AAC_DECODER)
    #error "Not Supported Stream Type"
#elif defined (CONFIG_AUDIO_SUPPORT_G711A_DECODER)
    sip_service_config.acodec_type = RTC_ACODEC_G711A;        
#else
    #error "Not Supported Stream Type"
#endif
    self->sip = esp_rtc_service_init(&sip_service_config);

    *handle = self;
    return ESP_OK;
}

esp_err_t voip_sip(voip_handle_t handle, esp_rtc_handle_t* out)
{
    if (!handle) return ESP_ERR_INVALID_STATE;
    *out = handle->sip;
    return ESP_OK;
}

esp_err_t voip_deinit(voip_handle_t handle)
{
    esp_err_t ret = ESP_OK;
    if (handle) {
        ret = esp_rtc_service_deinit(handle->sip);
        xEventGroupClearBits(handle->sent_evt, AUDIO_DATA_SENT);
        audio_free(handle);
    }
    return ret;
}

