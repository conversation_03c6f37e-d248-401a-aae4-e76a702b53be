#include <time.h>
#include <sys/time.h>
#include "freertos/idf_additions.h"
#include "board.h"
#include "esp_spiffs.h"
#include "../priv.h"
#include "../prompt.h"

typedef struct app_prompt {
    tone_play_callback_t    tone_cb;
    esp_dispatcher_handle_t esp_dispatcher;
} app_prompt_t;


esp_err_t prompt_init(prompt_handle_t* out, tone_play_callback_t callback)
{
    /** init the audio board */
    audio_board_handle_t board_handle = audio_board_init();
    audio_hal_ctrl_codec(board_handle->audio_hal, AUDIO_HAL_CODEC_MODE_BOTH, AUDIO_HAL_CTRL_START);
    audio_hal_set_volume(board_handle->audio_hal, 80);

    /* init the prompt partiion*/
     esp_vfs_spiffs_conf_t conf = {
      .base_path = "/spiffs",
      .partition_label = "spiffs_data",
      .max_files = 5,
      .format_if_mount_failed = false
    };
    ESP_ERROR_CHECK(esp_vfs_spiffs_register(&conf));

    while (!esp_spiffs_mounted("spiffs_data")) {
        vTaskDelay(500 / portTICK_PERIOD_MS);
    }
    app_prompt_t* inst = audio_calloc(1, sizeof(app_prompt_t));
    inst->esp_dispatcher = esp_dispatcher_get_delegate_handle();
    audio_tone_init(callback);
    *out = inst;
    return ESP_OK;
}

static esp_err_t dispatcher_audio_play(void *instance, action_arg_t *arg, action_result_t *result)
{
    audio_tone_play((char *)arg->data);
    return ESP_OK;
};

esp_err_t prompt_play(prompt_handle_t handle, const char* uri) 
{
    action_arg_t action_arg = {0};
    action_arg.data = (void *) uri;
    action_result_t result = {0};
    return esp_dispatcher_execute_with_func(handle->esp_dispatcher, dispatcher_audio_play, NULL, &action_arg, &result);
}

esp_err_t prompt_volume_set(prompt_handle_t handle, int volume) 
{
    audio_board_handle_t board = audio_board_get_handle(); 
    if (!board) return ESP_ERR_INVALID_STATE;
    return audio_hal_set_volume(board->audio_hal, volume);
}

esp_err_t prompt_play_default(prompt_handle_t handle) 
{
    return prompt_play(handle, "spiffs://spiffs/dingding.wav");
}