#include "../play.h"
#include "../priv.h"

typedef struct {
    char *frame_ptr;
    int frame_len;
} frame_package_t;

typedef struct app_play
{
    player_pipeline_handle_t   player_pipeline;
    QueueHandle_t              frame_q;
    bool                       data_proc_running;
#if defined(CONFIG_AUDIO_SUPPORT_OPUS_DECODER)
#define DEALULT_OPUS_DATA_CHACHE_SIZE (512)
    char                    *opus_data_cache;
    int                      opus_data_cache_len;
#endif  // CONFIF_AUDIO_SUPPORT_OPUS_DECODER    
} app_play_t;



esp_err_t play_feed(play_handle_t handle, const void* data_ptr, size_t data_len)
{
    ESP_LOGD(TAG, "remote audio data ");

    pipe_player_state_e state;
    player_pipeline_get_state(handle->player_pipeline, &state);
    if (state == PIPE_STATE_IDLE) {
        return ESP_ERR_INVALID_STATE;
    }
    frame_package_t frame = { 0 };
    frame.frame_ptr = audio_calloc(1, data_len);
    memcpy(frame.frame_ptr, data_ptr, data_len);
    frame.frame_len = data_len;
    return xQueueSend(handle->frame_q, &frame, pdMS_TO_TICKS(10)) == pdPASS ? ESP_OK : ESP_FAIL;
}

static void audio_pull_data_process(app_play_t *handle, char *ptr, int len)
{
    char *data_ptr = ptr;
    int data_len = len;
    /* Since OPUS is in VBR mode, it needs to be packaged into a length + data format first then to decoder*/
#if defined (CONFIG_AUDIO_SUPPORT_OPUS_DECODER)

#define frame_length_prefix (2)
    if (handle->opus_data_cache_len + frame_length_prefix < len) {
        handle->opus_data_cache = audio_realloc(handle->opus_data_cache, len + frame_length_prefix);
        handle->opus_data_cache_len = len;
    }
    handle->opus_data_cache[0] = (len >> 8) & 0xFF;
    handle->opus_data_cache[1] = len & 0xFF;
    memcpy(handle->opus_data_cache + frame_length_prefix, ptr, len);
    data_ptr = handle->opus_data_cache;
    data_len += frame_length_prefix;
#else
    data_ptr = ptr;
    data_len = len;
#endif // CONFIG_AUDIO_SUPPORT_OPUS_DECODER
    raw_stream_write(player_pipeline_get_raw_write(handle->player_pipeline), data_ptr, data_len);
}

static void audio_data_process_task(void *args)
{
    app_play_t *handle = (app_play_t *) args;
    frame_package_t frame = { 0 };
    handle->data_proc_running = true;
    while (handle->data_proc_running) {
        xQueueReceive(handle->frame_q, &frame, portMAX_DELAY);
        if (frame.frame_ptr) {
            audio_pull_data_process(handle, frame.frame_ptr, frame.frame_len);
            audio_free(frame.frame_ptr);
        }
    }
    vTaskDelete(NULL);
}


esp_err_t play_init(play_handle_t* out) 
{
    app_play_t* inst = audio_calloc(1, sizeof(app_play_t));
#if defined (CONFIG_AUDIO_SUPPORT_OPUS_DECODER)
    inst->opus_data_cache_len = DEALULT_OPUS_DATA_CHACHE_SIZE;
    inst->opus_data_cache = audio_calloc(1, inst->opus_data_cache_len);
#endif // CONFIG_AUDIO_SUPPORT_OPUS_DECODER

    inst->player_pipeline = player_pipeline_open();
    inst->frame_q = xQueueCreate(30, sizeof(frame_package_t));
    *out = inst;
    player_pipeline_run(inst->player_pipeline);
    vTaskDelay(pdMS_TO_TICKS(20));
    return audio_thread_create(NULL, "audio_data_process_task", audio_data_process_task, inst, 5 * 1024, 10, true, 0);
}

esp_err_t play_run(play_handle_t handle) 
{
    return player_pipeline_run(handle->player_pipeline);
}

esp_err_t play_stop(play_handle_t handle) 
{
    return player_pipeline_stop(handle->player_pipeline);
}

esp_err_t play_deinit(play_handle_t handle) 
{
    if (handle) {
        handle->data_proc_running = false;
        // TODO release all resources
    }
    return ESP_OK;
} 



