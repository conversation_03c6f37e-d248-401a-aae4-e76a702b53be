#pragma once

#include "audio_app.h"
#include <string.h>

#include "freertos/FreeRTOS.h"
#include "freertos/task.h"
#include "freertos/semphr.h"
#include "freertos/queue.h"

#include "esp_log.h"
#include "sdkconfig.h"
#include "esp_check.h"
#include "esp_timer.h"
#include "audio_recorder.h"
#include "recorder_sr.h"
#include "audio_pipeline.h"
#include "audio_thread.h"
#include "raw_stream.h"
#include "audio_mem.h"
#include "esp_delegate.h"
#include "esp_dispatcher.h"

#include "audio_processor.h"

#define  TAG  "audio_app"
