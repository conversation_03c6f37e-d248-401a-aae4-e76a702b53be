#pragma once
#include "esp_err.h"
#include "audio_recorder.h"

typedef struct app_record* record_handle_t;
typedef void (*on_data_ready_t)(uint8_t* pbuf, size_t sz);
esp_err_t record_init(record_handle_t* out, rec_event_cb_t rec_cb, on_data_ready_t data_cb);
esp_err_t record_start(record_handle_t handle);
esp_err_t record_notify_wakup(record_handle_t handle, bool up);
esp_err_t record_stop(record_handle_t handle);
