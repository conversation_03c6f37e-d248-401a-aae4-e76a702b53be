menu "Radar Audio Application"
    menu "Core Audio Configuration"
        choice
            prompt "Audio Conversation Trigger Mode"
            default LANGUAGE_WAKEUP_MODE

            config LANGUAGE_WAKEUP_MODE
                bool "Wake Word Mode"
                select SR_WN_WN9_XIAOAITONGXUE
                help
                    System is activated by a predefined wake word

            config CONTINUOUS_CONVERSATION_MODE
                bool "Continuous Mode"
                help
                    Keeps the system active after the initial trigger for ongoing conversation.

            config KEY_PRESS_DIALOG_MODE
                bool "Key Press Mode"
                help
                    System is triggered by a physical key press.
        endchoice

        choice
            prompt "Audio Codec"
            default AUDIO_SUPPORT_G711A_DECODER        
            config AUDIO_SUPPORT_OPUS_DECODER
                bool "OPUS"
            config AUDIO_SUPPORT_AAC_DECODER
                bool "ACC"
            config AUDIO_SUPPORT_G711A_DECODER
                bool "G711A"
        endchoice

    endmenu            

    menu "C4H"
        config C4H_SUPPORT_RAINMAKER
            bool "Enable Rainmaker Supprt"
            default n

        menu "VoIP Default Params"
            config C4H_DEFAULT_SIP_URI
                string "SIP URI"
                default "tcp://1001:<EMAIL>:7160"
                help
                    The SIP URI with username, password, address, port and transport type
            
            config C4H_DEFAULT_SIP_TARGET
                string "Comma Seperated Calling Targets"
                default "1006"
                help
                    The SIP peer number

            config C4H_DEFAULT_SIP_VOLUME
                int "Volume"
                default 90
                
        endmenu

    endmenu
    
endmenu
