#include "audio_app_priv.h"

#define WAIT_DESTORY_EVENT_BIT   (1 << 0)


#define audio_app_safe_free(x, fn) do {   \
    if (x) {                             \
        fn(x);                           \
        x = NULL;                        \
    }                                    \
} while (0)


static c4h_app_t* self = NULL;

static void audio_tone_player_event_cb(audio_element_status_t evt)
{
    if (evt == AEL_STATUS_STATE_FINISHED) {
        play_run(self->play);
    }
}

static esp_err_t rec_engine_cb(audio_rec_evt_t *event, void *user_data)
{
    if (AUDIO_REC_WAKEUP_START == event->type) {
        ESP_LOGI(TAG, "rec_engine_cb - AUDIO_REC_WAKEUP_START");
        play_stop(self->play);
        prompt_play_default(self->prompt);
        record_notify_wakup(self->rec, true);
        /* we call targets imediately when waken up */        
        audio_app_sip_call_target(self);
    } else if (AUDIO_REC_VAD_START == event->type) {
        ESP_LOGI(TAG, "rec_engine_cb - AUDIO_REC_VAD_START");
    } else if (AUDIO_REC_VAD_END == event->type) {
        ESP_LOGI(TAG, "rec_engine_cb - AUDIO_REC_VAD_END");
    } else if (AUDIO_REC_WAKEUP_END == event->type) {
        record_notify_wakup(self->rec, false);
        play_stop(self->play);
        ESP_LOGI(TAG, "rec_engine_cb - AUDIO_REC_WAKEUP_END");
    } else {
        ESP_LOGI(TAG, "rec_engine_cb - unknown %d", event->type);
    }

    return ESP_OK;
}

static void log_clear(void)
{
    esp_log_level_set("*", ESP_LOG_INFO);
    esp_log_level_set("AUDIO_THREAD", ESP_LOG_ERROR);
    esp_log_level_set("i2c_bus_v2", ESP_LOG_ERROR);
    esp_log_level_set("AUDIO_HAL", ESP_LOG_ERROR);
    esp_log_level_set("AUDIO_PIPELINE", ESP_LOG_ERROR);
    esp_log_level_set("AUDIO_ELEMENT", ESP_LOG_ERROR);
    esp_log_level_set("I2S_STREAM_IDF5.x", ESP_LOG_ERROR);
    esp_log_level_set("RSP_FILTER", ESP_LOG_ERROR);
    esp_log_level_set("AUDIO_EVT", ESP_LOG_ERROR);


    esp_log_level_set("AUDIO_PROCESSOR", ESP_LOG_ERROR);
    esp_log_level_set("SPIFFS_STREAM", ESP_LOG_ERROR);
    esp_log_level_set("DEC_WAV", ESP_LOG_ERROR);
    esp_log_level_set("WAV_DECODER", ESP_LOG_ERROR);
    esp_log_level_set("CODEC_ELEMENT_HELPER", ESP_LOG_ERROR);
    esp_log_level_set("AUDIO_RECORDER", ESP_LOG_ERROR);
    esp_log_level_set("MODEL_LOADER", ESP_LOG_ERROR);
    
    ESP_LOGI(TAG, "Clear the default log level");
}


static void _on_record_data_ready(uint8_t* pbuf, size_t sz)
{
    /* send the recorded data to the sip service only if it's been started */
    if (self->voip) {
        if (voip_send(self->voip, pbuf, sz) != ESP_OK) {
            ESP_LOGW(TAG, "sending voip data failed");
        }
    }
}

esp_err_t audio_app_init(audio_app_cfg_t* cfg)
{
    log_clear();   

    if (self) ESP_LOGE(TAG, "app is already initiated");
    self = audio_calloc(1, sizeof(c4h_app_t));
    
    /* notice, we hold a pointer to the config */
    self->cfg = cfg;
    
    self->wait_destory_event = xEventGroupCreate();
    /* the SIP service is started later*/
    self->voip = NULL;  

    prompt_init(&self->prompt, audio_tone_player_event_cb);
    play_init(&self->play);
    record_init(&self->rec, rec_engine_cb, _on_record_data_ready);
    vTaskDelay(pdMS_TO_TICKS(200));
    prompt_play_default(self->prompt);

#ifdef CONFIG_C4H_SUPPORT_RAINMAKER
    ESP_LOGI(TAG, "Enabling the rainmaker support");    
    ESP_RETURN_ON_ERROR(audio_app_rmaker_init(self), TAG, "Failed to init the rmaker part");
#endif    
    return ESP_OK;
}

esp_err_t audio_app_trigger(void) 
{
    return record_start(self->rec);
}

esp_err_t audio_app_deinit(void)
{
    if (self != NULL) {
        // TODO: more jobs on releasing all resources
        play_deinit(self->play);
        xEventGroupWaitBits(self->wait_destory_event, WAIT_DESTORY_EVENT_BIT, pdTRUE, pdTRUE, portMAX_DELAY);
        audio_app_safe_free(self->wait_destory_event, vEventGroupDelete);
        audio_free(self);
        self = NULL;
    }
    return ESP_OK;
}

static int _handle_sip_receive_audio(unsigned char *data, int len, void* ctx) 
{
    return play_feed(self->play, data, len);
}

esp_err_t audio_app_connect() 
{
    if (!self->voip) {
        ESP_LOGW(TAG, "a voip already exists");
        voip_deinit(self->voip);
        self->voip = NULL;
    }

    /* check the voip server URI*/
    if (!self->cfg->voip_uri) {
        ESP_LOGE(TAG, "the voip URI is invalid");
        return ESP_ERR_INVALID_ARG;
    }
    return voip_init(&self->voip, self->cfg->voip_uri, audio_app_sip_handle_event, _handle_sip_receive_audio, self);
}

/**
 * Disconnect to the server side resources
 */
esp_err_t audio_app_disconnect(void) 
{
    if (self->voip) {
        voip_deinit(self->voip);
        self->voip = NULL;
    }
    return ESP_OK;
}

