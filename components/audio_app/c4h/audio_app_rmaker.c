#include "audio_app_priv.h"
#include "esp_rmaker_standard_params.h"
#include "esp_rmaker_standard_devices.h"
#include "esp_rmaker_standard_types.h"


#define HELPME_DEVICE_NAME        "SoS"
#define HELPME_PARAM_URI          "URI"         /*!< "Transport://user:pass@server:port/path" */
#define HELPME_PARAM_TARGET       "Target"    
#define HELPME_PARAM_VOLUME       "Volume"        

static esp_err_t bulk_write_cb(const esp_rmaker_device_t *device, const esp_rmaker_param_write_req_t write_req[],
        uint8_t count, void *priv_data, esp_rmaker_write_ctx_t *ctx)
{
    if (!ctx) {
        ESP_LOGE(TAG, "Received write request via : UNKNOWN");
        return ESP_ERR_INVALID_ARG;
    } 
    /* NOTICE "Init" for ctx->src will be used when we add a new param of a device */
    ESP_LOGI(TAG, "Received write request via : %s %d", esp_rmaker_device_cb_src_to_str(ctx->src), count);

    for (int i = 0; i < count; i++) {
        const esp_rmaker_param_t *param = write_req[i].param;
        esp_rmaker_param_val_t val = write_req[i].val;
        const char *device_name = esp_rmaker_device_get_name(device);
        const char *param_name = esp_rmaker_param_get_name(param);

        if (strcmp(device_name, HELPME_DEVICE_NAME) == 0) {
            /* TODO check each param and returns ESP_ERR_INVALID_ARG if it is invalid*/
            if (strcmp(param_name, HELPME_PARAM_URI) == 0) {
                ESP_LOGE(TAG, "Received value = %s for %s - %s", val.val.s, device_name, param_name);
            }
        }
        esp_rmaker_param_update(param, val);
    }
    return ESP_OK;
}

static esp_rmaker_device_t* helpme_sos_device_add(c4h_app_t *self) 
{
    esp_rmaker_device_t *device = esp_rmaker_device_create(HELPME_DEVICE_NAME, NULL, self);
    esp_rmaker_device_add_bulk_cb(device, bulk_write_cb, NULL);
    
    /* NOTICE: call idf.py erase-flash if CONFIG_XXX is changed in the following code */
    if (device) {
        esp_rmaker_device_add_param(device, esp_rmaker_name_param_create(ESP_RMAKER_DEF_NAME_PARAM, HELPME_DEVICE_NAME));

        /* sip server that we are using */
        esp_rmaker_param_t *uri = esp_rmaker_param_create(
            HELPME_PARAM_URI, NULL, 
            esp_rmaker_str(CONFIG_C4H_DEFAULT_SIP_URI), 
            PROP_FLAG_READ | PROP_FLAG_WRITE | PROP_FLAG_PERSIST
        );
        esp_rmaker_device_add_param(device, uri);
        ESP_LOGE(TAG, "HELPME_PARAM_URI should be hidden when working with the public rainmaker or different VOIP operator");

        /* add the targets param */
        esp_rmaker_param_t *targets = esp_rmaker_param_create(
            HELPME_PARAM_TARGET, NULL,
            esp_rmaker_str(CONFIG_C4H_DEFAULT_SIP_TARGET),
            PROP_FLAG_READ | PROP_FLAG_WRITE | PROP_FLAG_PERSIST
        );    
        esp_rmaker_device_add_param(device, targets);

        /* add the volum parameter */
        esp_rmaker_param_t *volume = esp_rmaker_param_create(
            HELPME_PARAM_VOLUME, NULL, 
            esp_rmaker_int(CONFIG_C4H_DEFAULT_SIP_VOLUME), 
            PROP_FLAG_READ | PROP_FLAG_WRITE | PROP_FLAG_PERSIST
        );
        esp_rmaker_param_add_ui_type(volume, ESP_RMAKER_UI_SLIDER);
        esp_rmaker_param_add_bounds(volume, esp_rmaker_int(0), esp_rmaker_int(100), esp_rmaker_int(1));
        esp_rmaker_device_add_param(device, volume);

        esp_rmaker_node_add_device(esp_rmaker_get_node(), device);        
	}
    return device;
}


static void sync_maker_config(c4h_app_t* self, esp_rmaker_device_t *device) 
{
    /* use the customized uri and targets if they are present */
    esp_rmaker_param_val_t *uri     = esp_rmaker_param_get_val(esp_rmaker_device_get_param_by_name(device, HELPME_PARAM_URI));
    esp_rmaker_param_val_t *target  = esp_rmaker_param_get_val(esp_rmaker_device_get_param_by_name(device, HELPME_PARAM_TARGET));
    esp_rmaker_param_val_t *volume  = esp_rmaker_param_get_val(esp_rmaker_device_get_param_by_name(device, HELPME_PARAM_VOLUME));
    
    /* Point to our rmaker instance */
    self->cfg = &self->rcfg;

    /* Update the config values */
    if (uri->val.s != NULL && strlen(uri->val.s) > 0) {
        self->cfg->voip_uri = uri->val.s;
    }
    if (target->val.s != NULL && strlen(target->val.s) > 0) {
        self->cfg->voip_target = target->val.s;
    }
    prompt_volume_set(self->prompt, volume->val.i);
    ESP_LOGI(TAG, "Pointed the config to a new one");
}

esp_err_t audio_app_rmaker_init(c4h_app_t* self) 
{
    /* Add a  helpme config device */
    esp_rmaker_device_t* config = helpme_sos_device_add(self);
    ESP_RETURN_ON_ERROR(config == NULL ? ESP_FAIL : ESP_OK, TAG, "creating help config");

    sync_maker_config(self, config);
    return ESP_OK;
}