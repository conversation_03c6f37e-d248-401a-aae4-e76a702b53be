#pragma once

#include <string.h>

#include "freertos/FreeRTOS.h"
#include "freertos/task.h"
#include "freertos/semphr.h"
#include "freertos/queue.h"

#include "esp_log.h"
#include "sdkconfig.h"

#include "esp_timer.h"
#include "audio_recorder.h"
#include "recorder_sr.h"
#include "audio_pipeline.h"
#include "audio_thread.h"
#include "raw_stream.h"
#include "audio_mem.h"
#include "esp_delegate.h"
#include "esp_dispatcher.h"

#include "audio_processor.h"

#include "../core/prompt.h"
#include "../core/play.h"
#include "../core/record.h"
#include "../core/voip.h"
#include "../core/priv.h"

#include "audio_app.h"

typedef struct c4h_app {
    audio_app_cfg_t             *cfg;
    prompt_handle_t             prompt;
    play_handle_t               play;
    record_handle_t             rec;
    EventGroupHandle_t          wait_destory_event;
    voip_handle_t               voip;
    
#ifdef CONFIG_C4H_SUPPORT_RAINMAKER
    audio_app_cfg_t             rcfg;
#endif    
} c4h_app_t;


int  audio_app_sip_handle_event(esp_rtc_event_t event, void* ctx); 

/**
 * Call one or more targets
 */
void audio_app_sip_call_target(c4h_app_t* self); 

esp_err_t audio_app_rmaker_init(c4h_app_t* self); 