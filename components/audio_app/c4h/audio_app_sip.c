
#include "audio_app_priv.h"
#include "esp_rtc.h"

int audio_app_sip_handle_event(esp_rtc_event_t event, void* ctx) 
{
    c4h_app_t* self = (c4h_app_t*) ctx;

    /* we should use this callback to call a list of a target */
    ESP_LOGD(TAG, "_handle_sip_event event %d %d", event, self != NULL);
    switch ((int)event) {
        case ESP_RTC_EVENT_REGISTERED:
            ESP_LOGI(TAG, "ESP_RTC_EVENT_REGISTERED");
            break;
        case ESP_RTC_EVENT_UNREGISTERED:
            ESP_LOGI(TAG, "ESP_RTC_EVENT_UNREGISTERED");
            break;
        case ESP_RTC_EVENT_CALLING:
            ESP_LOGI(TAG, "ESP_RTC_EVENT_CALLING Remote Ring...");
            break;
        case ESP_RTC_EVENT_INCOMING:
            /* we should use a white list to accept a remote call */
            ESP_LOGI(TAG, "ESP_RTC_EVENT_INCOMING...");
            break;
        case ESP_RTC_EVENT_AUDIO_SESSION_BEGIN:
            ESP_LOGI(TAG, "ESP_RTC_EVENT_AUDIO_SESSION_BEGIN");
            break;
        case ESP_RTC_EVENT_AUDIO_SESSION_END:
            ESP_LOGI(TAG, "ESP_RTC_EVENT_AUDIO_SESSION_END");
            break;
        case ESP_RTC_EVENT_HANGUP:
            ESP_LOGI(TAG, "ESP_RTC_EVENT_HANGUP");
            break;
        case ESP_RTC_EVENT_ERROR:
            ESP_LOGI(TAG, "ESP_RTC_EVENT_ERROR");
            break;
    }
    return ESP_OK;;
} 


void audio_app_sip_call_target(c4h_app_t* self) 
{
    if (!self->voip) {
        ESP_LOGW(TAG, "voip is not initiated yet");
        return;
    }
    
    esp_rtc_handle_t rtc;
    if (voip_sip(self->voip, &rtc) != ESP_OK){
        ESP_LOGE(TAG, "RTC handle cannot be obtained");
        return;
    } 
    /* TODO lf: call multiple targets in the comma text format */
    ESP_LOGE(TAG, "calling %s", self->cfg->voip_target);
    esp_rtc_call(rtc, self->cfg->voip_target);
}