#pragma once

#include "stdbool.h"
#include "esp_err.h"

#ifdef __cplusplus
extern "C" {
#endif


typedef struct audio_app_cfg {
    const char* voip_uri;
    const char* voip_target;
} audio_app_cfg_t;


#define AUDIO_APP_DEFAULT_CFG()                             \
    {                                                       \
        .voip_uri       = CONFIG_C4H_DEFAULT_SIP_URI,       \
        .voip_target    = CONFIG_C4H_DEFAULT_SIP_TARGET,    \
    }

/**
 * @brief  Initialize the audio app module.
 *
 * @return
 *     - ESP_OK: Initialization was successful.
 *     - Other: Appropriate erro code indicating the failure reason.
 */
esp_err_t audio_app_init(audio_app_cfg_t* cfg);


/**
 * Connect to the server side resources like SIP or other services
 */
esp_err_t audio_app_connect();

/**
 * Disconnect to the server side resources
 */
esp_err_t audio_app_disconnect(void);

/**
 * trigger the audio app manually
 */
esp_err_t audio_app_trigger(void);

/**
 * @brief  Deinitialize the audio app module..
 *
 * @return
 *     - ESP_OK: Deinitialization was successful.
 */
esp_err_t audio_app_deinit(void);

#ifdef __cplusplus
}
#endif
