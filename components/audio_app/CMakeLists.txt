set(COMPONENT_SRCS 
    ./core/src/prompt.c
    ./core/src/play.c
    ./core/src/record.c
    ./core/src/voip.c
    ./c4h/audio_app.c
    ./c4h/audio_app_sip.c
)

set(COMPONENT_ADD_INCLUDEDIRS ./include)
set(COMPONENT_REQUIRES audio_processor esp_rainmaker)

if(CONFIG_C4H_SUPPORT_RAINMAKER)
    list(APPEND COMPONENT_SRCS ./c4h/audio_app_rmaker.c)
endif()

register_component()

spiffs_create_partition_image(spiffs_data spiffs FLASH_IN_PROJECT)