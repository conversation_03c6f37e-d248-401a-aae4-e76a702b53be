CONFIG_IDF_CMAKE=y
CONFIG_IDF_TARGET_ARCH_RISCV=y
CONFIG_IDF_TARGET_ARCH="riscv"
CONFIG_IDF_TARGET="esp32p4"
CONFIG_IDF_TARGET_ESP32P4=y
CONFIG_ESP_DEFAULT_CPU_FREQ_MHZ_360=y
CONFIG_ESP_DEFAULT_CPU_FREQ_MHZ=360
CONFIG_IDF_EXPERIMENTAL_FEATURES=y

#
# SPI RAM config
#
CONFIG_SPIRAM=y
CONFIG_SPIRAM_SPEED_200M=y
CONFIG_SPIRAM_ALLOW_STACK_EXTERNAL_MEMORY=y
#end of SPI RAM config

#
# Serial flasher config
#
CONFIG_ESPTOOLPY_FLASHSIZE_8MB=y
CONFIG_ESPTOOLPY_FLASHSIZE="8MB"
# end of Serial flasher config

#
# Partition Table
#
# CONFIG_PARTITION_TABLE_SINGLE_APP is not set
# CONFIG_PARTITION_TABLE_SINGLE_APP_LARGE is not set
# CONFIG_PARTITION_TABLE_TWO_OTA is not set
CONFIG_PARTITION_TABLE_CUSTOM=y
CONFIG_PARTITION_TABLE_CUSTOM_FILENAME="partitions.csv"
CONFIG_PARTITION_TABLE_FILENAME="partitions.csv"
CONFIG_PARTITION_TABLE_MD5=y
# end of Partition Table

#
# Audio HAL
#
CONFIG_ESP32_P4_FUNCTION_EV_BOARD=y
# end of Audio HAL

#
# DSP Library
#
CONFIG_DSP_OPTIMIZED=y
CONFIG_DSP_OPTIMIZATION=0
CONFIG_DSP_MAX_FFT_SIZE_4096=y
CONFIG_DSP_MAX_FFT_SIZE=4096
# end of DSP Library

#
# ESP Speech Recognition
#
CONFIG_MODEL_IN_FLASH=y
CONFIG_AFE_INTERFACE_V1=y
CONFIG_SR_WN_WN9_HILEXIN=y
CONFIG_SR_MN_CN_MULTINET7_QUANT=y
CONFIG_SR_MN_EN_NONE=y
# end of ESP Speech Recognition

CONFIG_SLAVE_IDF_TARGET_ESP32C6=y
CONFIG_ESP_WIFI_REMOTE_LIBRARY_HOSTED=y
