# The following lines of boilerplate have to be in your project's
# CMakeLists in this exact order for cmake to work correctly
cmake_minimum_required(VERSION 3.5)


# Let's use path relative to ADF
set(RMAKER_PATH $ENV{ADF_PATH}/../esp-rainmaker)

include($ENV{ADF_PATH}/CMakeLists.txt)
include($ENV{IDF_PATH}/tools/cmake/project.cmake)

# Add RainMaker components and its related application components
list(APPEND EXTRA_COMPONENT_DIRS ${RMAKER_PATH}/examples/common)

# make sure components are available
list(APPEND EXTRA_COMPONENT_DIRS ../../components)

add_compile_options (-fdiagnostics-color=always)

project(audio_app_example)
