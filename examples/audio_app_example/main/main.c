/*
 * ESPRESSIF MIT License
 *
 * Copyright (c) 2024 <ESPRESSIF SYSTEMS (SHANGHAI) CO., LTD>
 *
 * Permission is hereby granted for use on all ESPRESSIF SYSTEMS products, in which case,
 * it is free of charge, to any person obtaining a copy of this software and associated
 * documentation files (the "Software"), to deal in the Software without restriction, including
 * without limitation the rights to use, copy, modify, merge, publish, distribute, sublicense,
 * and/or sell copies of the Software, and to permit persons to whom the Software is furnished
 * to do so, subject to the following conditions:
 *
 * The above copyright notice and this permission notice shall be included in all copies or
 * substantial portions of the Software.
 *
 * THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
 * IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY, FITNESS
 * FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR
 * COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER
 * IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN
 * CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.
 *
 */

#include <stdio.h>
#include <string.h>

#include "freertos/FreeRTOS.h"
#include "freertos/event_groups.h"
#include "freertos/queue.h"
#include "freertos/task.h"

#include "esp_log.h"

#include "amrnb_encoder.h"
#include "amrwb_encoder.h"
#include "audio_element.h"
#include "audio_idf_version.h"
#include "audio_mem.h"
#include "audio_sys.h"

#include "audio_pipeline.h"
#include "audio_recorder.h"
#include "audio_thread.h"
#include "board.h"
#include "esp_audio.h"
#include "filter_resample.h"
#include "i2s_stream.h"
#include "mp3_decoder.h"
#include "periph_adc_button.h"
#include "periph_button.h"
#include "raw_stream.h"
#include "recorder_encoder.h"
#include "recorder_sr.h"
#include "tone_stream.h"
#include "es7210.h"
#include "model_path.h"

#include "wifi_service.h"
#include "smart_config.h"
#include "audio_app.h"


static char *TAG = "audio_app_demo";


esp_err_t periph_callback(audio_event_iface_msg_t *event, void *context)
{
    ESP_LOGD(TAG, "Periph Event received: src_type:%x, source:%p cmd:%d, data:%p, data_len:%d",
        event->source_type, event->source, event->cmd, event->data, event->data_len);
    switch (event->source_type) {
        case PERIPH_ID_ADC_BTN:
            if (((int)event->data == get_input_rec_id()) && (event->cmd == PERIPH_ADC_BUTTON_PRESSED)) {
                audio_app_trigger();
                ESP_LOGI(TAG, "REC KEY PRESSED");
            } else if (((int)event->data == get_input_rec_id()) && (event->cmd == PERIPH_ADC_BUTTON_RELEASE || event->cmd == PERIPH_ADC_BUTTON_LONG_RELEASE)) {
                // audio_app_recorder_force_stop();
                ESP_LOGI(TAG, "REC KEY RELEASE");
            }
            break;
        case PERIPH_ID_BUTTON:
            int key = (int)event->data;
            if (( key == get_input_mode_id()) && (event->cmd == PERIPH_BUTTON_PRESSED)) {
                audio_app_trigger();
                ESP_LOGI(TAG, "HELP KEY PRESSED");
            }           

            if ((key == get_input_rec_id()) && (event->cmd == PERIPH_BUTTON_PRESSED)) {
                //audio_app_recorder_force_start();
                ESP_LOGI(TAG, "HELP KEY SECOND PRESSED");
            } 
            break;
        default:
            break;
    }
    return ESP_OK;
}


void app_main(void)
{

    esp_periph_config_t periph_cfg = DEFAULT_ESP_PERIPH_SET_CONFIG();
    periph_cfg.extern_stack = true;
    esp_periph_set_handle_t set = esp_periph_set_init(&periph_cfg);
    if (set != NULL) {
        esp_periph_set_register_callback(set, periph_callback, NULL);
    }

    audio_board_key_init(set);
    audio_app_cfg_t cfg = AUDIO_APP_DEFAULT_CFG();
    if (audio_app_init(&cfg) != ESP_OK) {
        ESP_LOGE(TAG, "Init audio app failed");
    } else {
        ESP_LOGI(TAG, "---Init audio app done---");
    }

}
