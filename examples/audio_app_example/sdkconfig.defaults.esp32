#
# Serial flasher config
#
CONFIG_ESPTOOLPY_FLASHSIZE_8MB=y
# end of Serial flasher config

#
# Partition Table
#
# CONFIG_PARTITION_TABLE_SINGLE_APP is not set
# CONFIG_PARTITION_TABLE_SINGLE_APP_LARGE is not set
# CONFIG_PARTITION_TABLE_TWO_OTA is not set
CONFIG_PARTITION_TABLE_CUSTOM=y
CONFIG_PARTITION_TABLE_CUSTOM_FILENAME="partitions_esp32.csv"
CONFIG_PARTITION_TABLE_FILENAME="partitions_esp32.csv"
CONFIG_PARTITION_TABLE_MD5=y
# end of Partition Table

#
# Audio HAL
#
CONFIG_ESP_LYRAT_MINI_V1_1_BOARD=y

#
# Audio Recorder
#

# end of Audio Recorder

#
# ESP32-specific
#
CONFIG_ESP32_ECO3_CACHE_LOCK_FIX=y
CONFIG_ESP32_REV_MIN_0=y
CONFIG_ESP32_REV_MIN=0
CONFIG_ESP32_DEFAULT_CPU_FREQ_240=y
CONFIG_ESP32_DEFAULT_CPU_FREQ_MHZ=240
CONFIG_ESP32_SPIRAM_SUPPORT=y

#
# SPI RAM config
#
CONFIG_SPIRAM_SPEED_80M=y
CONFIG_SPIRAM=y
CONFIG_SPIRAM_BOOT_INIT=y
CONFIG_SPIRAM_ALLOW_BSS_SEG_EXTERNAL_MEMORY=y
CONFIG_SPIRAM_CACHE_WORKAROUND=y
CONFIG_SPIRAM_MALLOC_ALWAYSINTERNAL=4096
CONFIG_SPIRAM_ALLOW_STACK_EXTERNAL_MEMORY=y


#
# ESP Speech Recognition
#
CONFIG_SR_MN_CN_NONE=y
CONFIG_SR_MN_EN_NONE=y
# end of ESP Speech Recognition