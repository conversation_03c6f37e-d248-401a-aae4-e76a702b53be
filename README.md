# Usage

Steps of running any example or firmwares in any project in **esp-adf (8301b97d70d523051897546f639273b7706ddb4d) ** or this project:
1. Activate the 'esp-adf' environment by ```cd ~/esp/esp-adf && . ./export.sh```
2. Goto the particular directory such as ```examples``` or ```firmwares```
3. Make sure the **Custom audio board** is selected in the **Audio HAL**  in the main menu
4. Make sure the right radar board is selected in the **Radar Board** in the main menu
5. ```idf.py build flash monitor```

Before running projects in esp-adf, ***the following line must be added*** into the ```xxx_project/CMakeList.txt``` in order to make sure our board (defined in the ```components``` of this project) can be found:
```
include($ENV{IDF_PATH}/tools/cmake/project.cmake)

# make sure components are available
list(APPEND EXTRA_COMPONENT_DIRS $ENV{ADF_PATH}/../../repo/vpc/rfc/esp-radar/components)
...
```
That is like what we add in the examples in this project
```
include($ENV{ADF_PATH}/CMakeLists.txt)
include($ENV{IDF_PATH}/tools/cmake/project.cmake)
# make sure components are available
list(APPEND EXTRA_COMPONENT_DIRS ../../../components)
project(voip_audio_only)
```

## Board Examples

All examples in the ```examples/board``` are used to test the basic functions and should be tested if:
1. update to a new version of ADF
2. add a new board.

Meanwhile, test audio quality with a real and well closed product, rather than a naked board.

### play audio (t01_play_flash_mp3)
It's used to test whether the decoder of a board works property and run the ```esp-adf/examples/player/pipeline_embed_flash_tone```
1. Just run it normally 
2. Listen the prompt voice

### record audio (t02_record_over_http)
It's used to test whether the encoder of a board works property and run the ```esp-adf/examples/recorder/pipeline_raw_http```
1. Run the server ```cd utils && python server --ip *************```
2. General steps and ```idf.py flash monitor```

### voip  (t03_voip in this project only)
It's used to test rtc based communication based on well configured codec
1. Configure the sip account and the **peer number** in its ```Kconfig.projbuild```
2. Press button to call the peer number (on your phone)
3. The board will answer the call from any one **without** pressing any button

### speech recgonition (t04_wwe)
It's used to test the voice speech recgonition and run the ```esp-adf/examples/speech_recognition/wwe```, actually the ```vad``` in its sibling folder also works with our board. 

Errors may occur if we are running on the pro version since its flash is QUAD. We may use a low cost mode 
```
W (83659) AFE_SR: ERROR! afe_feed_aec_init_false, rb_out slow!!!
``` 


# Features

This platform support features

* Voip Support 
* Radar Interface
* Home Automation

In order to support these scenarios:
1. Voice Help Trigger by Button (supported)
1. **Voice Help Trigger Offline Voice (ESP 小爱同学)**
1. Voice Help Trigger Offline Voice (CI)


# Develop

## New Board
The radar board component contains all radar boards that we are using and each series and board **xxx** shold be defined in steps like the pro version:

0. Add a menu item in ```Kconfig.projbuild``` for example **RADAR_BOARD_XXX**
0. Copy the template file **veb_io_def.h** as a new one such as veb_xxx_io_def in ```radar_board/rbs/``` and then define IOs for this board.
0. Include the **veb_xxx_io_def.h** in ```board_def.h``` for **CONFIG_RADAR_BOARD_XXX** and define the board name as well.
0. Check each section starting with ***BOARD->*** in the board_def.h and add the xxx board's specification.
0. Run each demo in the ```examples/board``` folder in order to make sure xxx is correctly defined.

## Port ADF example
Steps of porting XXX in the esp-adf examples directory:
1. Copy the whole directory into ```samples```
2. Make sure ```components``` (the directory sibling to README.md) included in its CMakeLists.txt:
``` 
list(APPEND EXTRA_COMPONENT_DIRS ../../components)
...
project(XXX)
```
3. Add CONFIG_AUDIO_BOARD_CUSTOM=y in the sdkconfig.defaults
4. Remove all all boards selection (Audio HAL) in sdkconfig.xxx.

Make sure STEP 2 is performed, otherwise you will see a couple compiling erros or build erros like:
```
component_requirements.py: cannot match original component filename for source component esp_peripherals
```

## TODO

### Board
0. **The play volume is not large enough** in the play_flash_mp3 example and in voip_audio_only and the board gain in our audio board should be adjusted, or the pipeline_passthru may be better starting point.
0. Track esp-adf with git submodule for a particular working version (current is ```f8b77be63247fb8cb527a0c80a5750e88572052b```)
0. Try downsampling 8000 from the hardware codec (test its quality of recording and playing, as well as voip)
0. Check each board's hardware performance especially [the echo cancellation](https://docs.espressif.com/projects/esp-sr/en/latest/esp32s3/audio_front_end/Espressif_Microphone_Design_Guidelines.html#echo-reference-signal-design-suggestion) 
0. Rework on the [psram setting](https://espressif-docs.readthedocs-hosted.com/projects/esp-adf/en/latest/design-guide/design-considerations.html#optimization-of-internal-ram-and-use-of-psram)
0. Prepare and test ```speech_recognition/vad``` and ```speech_recognition/wwe```
0. Different PRO boards (with or without audio)
0. 4G support
0. Support PIEB (PIE Base Board with 8388)
0. Test **SMI LAN8720A(2 lines)** Ethernet Support [REF](https://blog.csdn.net/qq_41126242/article/details/144312515)
0. Prepare and test ```cloud_services/pipeline_baidu_speech_mp3``` and ```cloud_services/pipeline_aws_polly_mp3```
0. Remove the boring dispatch ***error*** with functions like ```esp_dispatcher_execute_with_func``` in ```nvs_dispatcher``` demo.  
0. Move the key and led service into board?? (tang)
0. Bring Audio HAL back. the Audio Custom Board plays a role of abstracting the audio hardware, but involves too many other hardware functions like keys or
diplays. The menu name 'Audio HAL' reflects is original design proposal and its design is fucked by the real usage.

### Component (tang)
0. Support dynamic sampling rate configuration of hardware sampling in av_stream
0. Support help speech for different languages with semi online support?
0. Support voice instructions in different languages
0. Support rainbow definition


### Firmwares
0. Add the manufacturing 
